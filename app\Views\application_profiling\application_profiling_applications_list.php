<?= $this->extend('templates/nolstemp') ?>

<?= $this->section('content') ?>

<div class="container-fluid px-4">
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="<?= base_url('profile_applications_exercise') ?>">Profiling Exercises</a></li>
            <li class="breadcrumb-item"><a href="<?= base_url('profile_applications_exercise/profile_exercise/' . ($position['exercise_id'] ?? '#')) ?>"><?= esc($position['exercise_name'] ?? 'Exercise') ?></a></li>
            <li class="breadcrumb-item"><a href="<?= base_url('profile_applications_exercise/profile_group/' . ($position['position_group_id'] ?? '#')) ?>"><?= esc($position['group_name'] ?? 'Group') ?></a></li>
            <li class="breadcrumb-item active" aria-current="page"><?= esc($position['designation'] ?? 'Position') ?></li>
        </ol>
    </nav>

    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
            <h1 class="m-0 font-weight-bold text-primary"><?= esc($title ?? 'Applications') ?></h1>
            <a href="<?= base_url('profile_applications_exercise/profile_group/' . ($position['position_group_id'] ?? '#')) ?>" class="btn btn-sm btn-secondary">
                <i class="fas fa-arrow-left me-1"></i> Back to Positions
            </a>
        </div>
        <div class="card-body">
            <?php if (session()->has('message')): ?>
                <div class="alert alert-success" role="alert">
                    <?= session('message') ?>
                </div>
            <?php endif; ?>
            <?php if (session()->has('error')): ?>
                <div class="alert alert-danger" role="alert">
                    <?= session('error') ?>
                </div>
            <?php endif; ?>

            <!-- Position Information -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card border-left-info">
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-8">
                                    <h5 class="card-title text-info"><?= esc($position['designation']) ?></h5>
                                    <p class="card-text">
                                        <strong>Reference:</strong> <?= esc($position['position_reference']) ?><br>
                                        <strong>Classification:</strong> <?= esc($position['classification']) ?><br>
                                        <strong>Location:</strong> <?= esc($position['location']) ?><br>
                                        <strong>Salary:</strong> <?= esc($position['annual_salary']) ?>
                                    </p>
                                </div>
                                <div class="col-md-4 text-end">
                                    <i class="fas fa-file-alt fa-3x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <p class="mb-4">Select an application to view the applicant's profile.</p>

            <?php if (!empty($applications) && is_array($applications)): ?>
                <div class="table-responsive">
                    <table class="table table-bordered table-striped" id="applications-table" width="100%" cellspacing="0">
                        <thead>
                            <tr>
                                <th>App #</th>
                                <th>Name</th>
                                <th>Gender</th>
                                <th>Location</th>
                                <th>Current Position</th>
                                <th>Submitted</th>
                                <th>Pre-Screen Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($applications as $app): ?>
                                <tr>
                                    <td><?= esc($app['application_number']) ?></td>
                                    <td>
                                        <strong><?= esc($app['first_name'] . ' ' . $app['last_name']) ?></strong>
                                        <br>
                                        <small class="text-muted"><?= esc($app['contact_details']) ?></small>
                                    </td>
                                    <td><?= esc($app['gender']) ?></td>
                                    <td><?= esc($app['place_of_origin']) ?></td>
                                    <td>
                                        <?= esc($app['current_position']) ?>
                                        <br>
                                        <small class="text-muted"><?= esc($app['current_employer']) ?></small>
                                    </td>
                                    <td><?= esc(date('d M Y H:i', strtotime($app['created_at']))) ?></td>
                                    <td>
                                        <?php if ($app['pre_screened_status'] == 'passed'): ?>
                                            <span class="badge badge-success">Passed</span>
                                            <br>
                                            <small class="text-muted"><?= esc(date('d M Y', strtotime($app['pre_screened']))) ?></small>
                                        <?php elseif ($app['pre_screened_status'] == 'failed'): ?>
                                            <span class="badge badge-danger">Failed</span>
                                        <?php else: ?>
                                            <span class="badge badge-warning">Pending</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <a href="<?= base_url('profile_applications_exercise/profile_view_employee/' . $app['id']) ?>" 
                                           class="btn btn-primary btn-sm" title="View Profile">
                                            <i class="fas fa-user"></i> View Profile
                                        </a>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php else: ?>
                <div class="text-center py-5">
                    <i class="fas fa-file-alt fa-3x text-gray-300 mb-3"></i>
                    <h4 class="text-gray-500">No Applications Available</h4>
                    <p class="text-gray-400">There are currently no applications available for this position.</p>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<!-- Include DataTables if needed -->
<link href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css" rel="stylesheet">
<script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>

<script>
    $(document).ready(function() {
        $('#applications-table').DataTable({
            "order": [[ 5, "desc" ]], // Order by submission date descending by default
            "pageLength": 25,
            "responsive": true
        });
    });
</script>
<?= $this->endSection() ?>
