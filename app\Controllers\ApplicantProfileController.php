<?php

namespace App\Controllers;

class ApplicantProfileController extends BaseController
{
    protected $session;

    public function __construct()
    {
        helper(['form', 'url', 'application']);
        $this->session = session();
    }

    /**
     * Display list of exercises for profiling (GET)
     */
    public function index()
    {
        // Mock exercises data based on ExerciseModel structure
        $exercises = [
            [
                'id' => 1,
                'exercise_name' => 'IT Recruitment Exercise 2024',
                'advertisement_no' => 'ADV-2024-001',
                'exercise_year' => '2024',
                'department' => 'Information Technology',
                'status' => 'selection',
                'application_count' => 25,
                'publish_date_from' => '2024-01-10',
                'publish_date_to' => '2024-02-10',
                'gazzetted_no' => 'GAZ-2024-001',
                'gazzetted_date' => '2024-01-01',
                'advertisement_date' => '2024-01-05',
                'mode_of_advertisement' => 'Online and Print Media',
                'description' => 'Annual IT recruitment exercise for various technical positions',
                'created_at' => '2024-01-01 08:00:00'
            ],
            [
                'id' => 2,
                'exercise_name' => 'Finance Department Recruitment 2024',
                'advertisement_no' => 'ADV-2024-002',
                'exercise_year' => '2024',
                'department' => 'Finance',
                'status' => 'selection',
                'application_count' => 18,
                'publish_date_from' => '2024-01-15',
                'publish_date_to' => '2024-02-15',
                'gazzetted_no' => 'GAZ-2024-002',
                'gazzetted_date' => '2024-01-05',
                'advertisement_date' => '2024-01-10',
                'mode_of_advertisement' => 'Online and Print Media',
                'description' => 'Annual finance department recruitment exercise',
                'created_at' => '2024-01-05 09:00:00'
            ],
            [
                'id' => 3,
                'exercise_name' => 'Administrative Services Recruitment 2024',
                'advertisement_no' => 'ADV-2024-003',
                'exercise_year' => '2024',
                'department' => 'Administrative Services',
                'status' => 'selection',
                'application_count' => 32,
                'publish_date_from' => '2024-01-20',
                'publish_date_to' => '2024-02-20',
                'gazzetted_no' => 'GAZ-2024-003',
                'gazzetted_date' => '2024-01-10',
                'advertisement_date' => '2024-01-15',
                'mode_of_advertisement' => 'Online and Print Media',
                'description' => 'Annual administrative services recruitment exercise',
                'created_at' => '2024-01-10 09:00:00'
            ]
        ];

        $data = [
            'title' => 'Exercises Available for Profiling',
            'menu' => 'profiling',
            'exercises' => $exercises
        ];

        return view('application_profiling/application_profiling_exercise_list', $data);
    }

    /**
     * Display positions for a specific exercise (GET)
     */
    public function exercisePositions($exerciseId)
    {
        // Mock exercise data based on ExerciseModel structure
        $exercise = [
            'id' => $exerciseId,
            'org_id' => 1,
            'exercise_name' => $exerciseId == 1 ? 'IT Recruitment Exercise 2024' : ($exerciseId == 2 ? 'Finance Department Recruitment 2024' : 'Administrative Services Recruitment 2024'),
            'gazzetted_no' => 'GAZ-2024-00' . $exerciseId,
            'gazzetted_date' => '2024-01-01',
            'advertisement_no' => 'ADV-2024-00' . $exerciseId,
            'advertisement_date' => '2024-01-05',
            'mode_of_advertisement' => 'Online and Print Media',
            'publish_date_from' => '2024-01-10',
            'publish_date_to' => '2024-02-10',
            'description' => 'Annual recruitment exercise for various positions',
            'status' => 'selection',
            'created_at' => '2024-01-01 08:00:00'
        ];

        // Mock positions data with position group information
        $positions = [
            [
                'id' => 1,
                'exercise_id' => $exerciseId,
                'position_reference' => 'IT-001',
                'designation' => 'Senior Software Developer',
                'position_group_name' => 'Information Technology',
                'classification' => 'Grade 12',
                'award' => 'K65,000 - K75,000',
                'location' => 'Port Moresby, NCD',
                'applications_count' => 8,
                'pending_prescreen_count' => 3,
                'passed_prescreen_count' => 5,
                'failed_prescreen_count' => 0
            ],
            [
                'id' => 2,
                'exercise_id' => $exerciseId,
                'position_reference' => 'IT-002',
                'designation' => 'Database Administrator',
                'position_group_name' => 'Information Technology',
                'classification' => 'Grade 11',
                'award' => 'K55,000 - K65,000',
                'location' => 'Port Moresby, NCD',
                'applications_count' => 6,
                'pending_prescreen_count' => 2,
                'passed_prescreen_count' => 4,
                'failed_prescreen_count' => 0
            ],
            [
                'id' => 3,
                'exercise_id' => $exerciseId,
                'position_reference' => 'IT-003',
                'designation' => 'Network Administrator',
                'position_group_name' => 'Information Technology',
                'classification' => 'Grade 10',
                'award' => 'K45,000 - K55,000',
                'location' => 'Port Moresby, NCD',
                'applications_count' => 5,
                'pending_prescreen_count' => 1,
                'passed_prescreen_count' => 4,
                'failed_prescreen_count' => 0
            ],
            [
                'id' => 4,
                'exercise_id' => $exerciseId,
                'position_reference' => 'IT-004',
                'designation' => 'Systems Analyst',
                'position_group_name' => 'Information Technology',
                'classification' => 'Grade 11',
                'award' => 'K55,000 - K65,000',
                'location' => 'Lae, Morobe',
                'applications_count' => 4,
                'pending_prescreen_count' => 1,
                'passed_prescreen_count' => 3,
                'failed_prescreen_count' => 0
            ],
            [
                'id' => 5,
                'exercise_id' => $exerciseId,
                'position_reference' => 'IT-005',
                'designation' => 'IT Support Specialist',
                'position_group_name' => 'Information Technology',
                'classification' => 'Grade 9',
                'award' => 'K35,000 - K45,000',
                'location' => 'Mount Hagen, WHP',
                'applications_count' => 2,
                'pending_prescreen_count' => 0,
                'passed_prescreen_count' => 2,
                'failed_prescreen_count' => 0
            ]
        ];

        $data = [
            'title' => 'Positions for Profiling',
            'menu' => 'profiling',
            'exercise' => $exercise,
            'positions' => $positions
        ];

        return view('application_profiling/application_profiling_exercise_positions', $data);
    }

    /**
     * Display position profile with applicant profiles in table format (GET)
     */
    public function positionProfile($positionId)
    {
        // Mock position data
        $position = [
            'id' => $positionId,
            'exercise_id' => 1,
            'exercise_name' => 'IT Recruitment Exercise 2024',
            'position_reference' => 'IT-001',
            'designation' => 'Senior Software Developer',
            'position_group_name' => 'Information Technology',
            'classification' => 'Grade 12',
            'award' => 'K65,000 - K75,000',
            'location' => 'Port Moresby, NCD',
            'qualifications' => 'Bachelor degree in Computer Science or related field',
            'knowledge' => 'Software development, database management, system analysis',
            'skills_competencies' => 'Programming languages, project management, team leadership',
            'job_experiences' => 'Minimum 5 years software development experience'
        ];

        // Mock applicant profiles data based on AppxApplicationProfileModel structure
        $applicantProfiles = [
            [
                'id' => 1,
                'application_id' => 1,
                'name' => 'John Michael Doe',
                'sex' => 'Male',
                'age' => 34,
                'place_origin' => 'Port Moresby, NCD',
                'contact_details' => '+675 123 4567, <EMAIL>',
                'nid_number' => 'NID123456789',
                'current_employer' => 'TechSoft PNG Ltd',
                'current_position' => 'Software Developer',
                'address_location' => '123 Main Street, Port Moresby',
                'qualification_text' => 'Bachelor of Computer Science, University of PNG (2012)',
                'other_trainings' => 'Certified Java Developer (2018), AWS Cloud Practitioner (2020)',
                'knowledge' => 'Java, Python, JavaScript, Database Design, Cloud Computing',
                'skills_competencies' => 'Full-stack development, Team leadership, Agile methodologies',
                'job_experiences' => 'Software Developer at TechSoft PNG (2015-Present), Junior Developer at DataSys (2013-2015)',
                'publications' => 'Co-authored "Modern Web Development in PNG" (2021)',
                'awards' => 'Best Developer Award - TechSoft PNG (2020)',
                'referees' => 'Mr. Peter Smith, CTO, TechSoft PNG, +************',
                'comments' => 'Excellent technical skills with strong leadership potential',
                'remarks' => 'Highly recommended for senior position'
            ],
            [
                'id' => 2,
                'application_id' => 2,
                'name' => 'Sarah Jane Wilson',
                'sex' => 'Female',
                'age' => 29,
                'place_origin' => 'Lae, Morobe',
                'contact_details' => '+************, <EMAIL>',
                'nid_number' => 'NID987654321',
                'current_employer' => 'Digital Solutions PNG',
                'current_position' => 'Senior Programmer',
                'address_location' => '456 Oak Avenue, Lae',
                'qualification_text' => 'Bachelor of Information Technology, PNG University of Technology (2016)',
                'other_trainings' => 'Microsoft Azure Certification (2019), Scrum Master Certification (2021)',
                'knowledge' => 'C#, .NET Framework, SQL Server, Azure Cloud Services',
                'skills_competencies' => 'Software architecture, Database optimization, Project management',
                'job_experiences' => 'Senior Programmer at Digital Solutions PNG (2018-Present), Programmer at CodeCraft (2016-2018)',
                'publications' => '',
                'awards' => 'Innovation Award - Digital Solutions PNG (2021)',
                'referees' => 'Ms. Mary Johnson, Manager, Digital Solutions PNG, +************',
                'comments' => 'Strong technical background with excellent problem-solving skills',
                'remarks' => 'Suitable for senior technical role'
            ],
            [
                'id' => 3,
                'application_id' => 3,
                'name' => 'Robert James Kila',
                'sex' => 'Male',
                'age' => 31,
                'place_origin' => 'Mount Hagen, WHP',
                'contact_details' => '+************, <EMAIL>',
                'nid_number' => 'NID456789123',
                'current_employer' => 'Government IT Department',
                'current_position' => 'Systems Analyst',
                'address_location' => '789 Pine Road, Mount Hagen',
                'qualification_text' => 'Bachelor of Computer Science, Divine Word University (2014)',
                'other_trainings' => 'CISSP Certification (2020), ITIL Foundation (2019)',
                'knowledge' => 'System analysis, Network security, Database administration',
                'skills_competencies' => 'Systems design, Security implementation, Documentation',
                'job_experiences' => 'Systems Analyst at Government IT (2017-Present), IT Support at TechServ (2015-2017)',
                'publications' => '',
                'awards' => 'Outstanding Service Award - Government IT (2022)',
                'referees' => 'Mr. David Brown, Director, Government IT, +675 555 6666',
                'comments' => 'Solid government experience with security focus',
                'remarks' => 'Good candidate with relevant government experience'
            ]
        ];

        $data = [
            'title' => 'Position Profile: ' . $position['designation'],
            'menu' => 'profiling',
            'position' => $position,
            'applicant_profiles' => $applicantProfiles
        ];

        return view('application_profiling/application_profiling_position_profile', $data);
    }

    /**
     * Display detailed profile view for an applicant (GET)
     */
    public function profileViewEmployee($applicationId)
    {
        // Mock application data
        $application = [
            'id' => $applicationId,
            'org_id' => 1,
            'exercise_id' => 1,
            'exercise_name' => 'Public Service Commission Recruitment 2024',
            'applicant_id' => 101,
            'position_id' => 1,
            'position_designation' => 'Administrative Officer Grade 1',
            'position_reference' => 'ADM-001',
            'application_number' => 'APP-2024-001',
            'first_name' => 'John',
            'last_name' => 'Doe',
            'gender' => 'Male',
            'date_of_birth' => '1990-05-15',
            'place_of_origin' => 'Port Moresby, NCD',
            'contact_details' => '+675 123 4567',
            'location_address' => '123 Main Street, Port Moresby',
            'current_employer' => 'ABC Company',
            'current_position' => 'Assistant Administrator',
            'current_salary' => 'K35,000',
            'citizenship' => 'Papua New Guinea',
            'marital_status' => 'Single',
            'application_status' => 'submitted',
            'created_at' => '2024-01-25 09:30:00'
        ];

        // Mock profile data based on AppxApplicationProfileModel structure
        $profile = [
            'id' => 1,
            'application_id' => $applicationId,
            'name' => 'John Doe',
            'sex' => 'Male',
            'age' => 34,
            'place_origin' => 'Port Moresby, NCD',
            'contact_details' => '+675 123 4567, <EMAIL>',
            'nid_number' => 'NID123456789',
            'current_employer' => 'ABC Company',
            'current_position' => 'Assistant Administrator',
            'address_location' => '123 Main Street, Port Moresby',
            'qualification_text' => 'Bachelor of Business Administration from University of Papua New Guinea (2012). Diploma in Public Administration from PNG Institute of Public Administration (2014).',
            'other_trainings' => 'Certificate in Project Management (2018), Advanced Computer Skills Training (2019), Leadership Development Program (2020)',
            'knowledge' => 'Extensive knowledge in administrative procedures, government policies and regulations, financial management, human resource management, and project coordination. Familiar with PNG public service systems and procedures.',
            'skills_competencies' => 'Strong analytical and problem-solving skills, excellent written and verbal communication, proficient in Microsoft Office Suite, database management, report writing, stakeholder engagement, and team leadership.',
            'job_experiences' => 'Assistant Administrator at ABC Company (2015-Present): Managed daily administrative operations, coordinated projects, supervised junior staff. Administrative Clerk at XYZ Department (2013-2015): Handled correspondence, maintained records, assisted in policy implementation.',
            'publications' => 'Co-authored "Improving Administrative Efficiency in PNG Public Service" published in PNG Administrative Review (2021)',
            'awards' => 'Employee of the Year - ABC Company (2020), Excellence in Public Service Award (2019)',
            'referees' => 'Mr. Peter Smith, Manager, ABC Company, +************. Ms. Mary Johnson, Director, XYZ Department, +************.',
            'comments' => 'Highly motivated individual with strong commitment to public service. Demonstrates excellent leadership potential and analytical skills.',
            'remarks' => 'Recommended for interview. Strong candidate with relevant experience and qualifications.',
            'created_at' => '2024-02-05 10:30:00',
            'updated_at' => '2024-02-05 10:30:00'
        ];

        $data = [
            'title' => 'Profile: ' . $application['first_name'] . ' ' . $application['last_name'],
            'menu' => 'profiling',
            'application' => $application,
            'profile' => $profile
        ];

        return view('application_profiling/application_profiling_profile_view', $data);
    }

    public function profile()
    {
        $applicant_id = session()->get('applicant_id');
        $applicant = $this->applicantsModel->find($applicant_id);

        if (!$applicant) {
            return redirect()->to('/')->with('error', 'Applicant not found');
        }

        // Get work experiences
        $experiences = $this->experiencesModel->where('applicant_id', $applicant_id)
                                            ->orderBy('date_from', 'DESC')
                                            ->findAll();

        // Get applicant's education records using automatic model features
        $education = $this->applicantEducationModel->where('applicant_id', $applicant_id)
                                                 ->orderBy('date_from', 'DESC')
                                                 ->findAll();

        // Get education levels from adx_education table for dropdowns
        $education_data = $this->educationModel->findAll();

        // Get applicant's files
        $files = $this->applicantFilesModel->where('applicant_id', $applicant_id)
                                          ->orderBy('created_at', 'DESC')
                                          ->findAll();

        $data = [
            'title' => 'Edit Profile',
            'menu' => 'profile',
            'applicant' => $applicant,
            'experiences' => $experiences,
            'education' => $education,
            'education_data' => $education_data,
            'files' => $files,
            'education_levels' => [
                1 => 'Elementary',
                2 => 'High School',
                3 => 'Vocational',
                4 => 'College',
                5 => 'Post Graduate'
            ]
        ];

        return view('applicant/applicant_profile', $data);
    }

    public function uploadPhoto()
    {
        $applicant_id = session()->get('applicant_id');
        $photo = $this->request->getFile('id_photo');

        // Debug response for troubleshooting
        if (!$photo || !$photo->isValid() || $photo->hasMoved()) {
            $debug = [
                'photo_exists' => !empty($photo),
                'is_valid' => $photo ? $photo->isValid() : false,
                'has_moved' => $photo ? $photo->hasMoved() : false,
                'error' => $photo ? $photo->getError() : 'No file uploaded'
            ];

            $response = [
                'success' => false,
                'message' => 'Invalid file upload',
                'debug' => $debug
            ];
            return $this->response->setJSON($response);
        }

        // Validate file type
        $allowedTypes = ['image/jpeg', 'image/png', 'image/jpg'];
        if (!in_array($photo->getMimeType(), $allowedTypes)) {
            $response = [
                'success' => false,
                'message' => 'Only JPG, JPEG, and PNG files are allowed',
                'debug' => ['mime_type' => $photo->getMimeType()]
            ];
            return $this->response->setJSON($response);
        }

        // Create upload directory if it doesn't exist
        $uploadPath = FCPATH . 'uploads/photos';
        if (!is_dir($uploadPath)) {
            mkdir($uploadPath, 0777, true);
        }

        try {
            // Debug info
            $debug = [
                'upload_path_exists' => is_dir($uploadPath),
                'upload_path_writable' => is_writable($uploadPath),
                'upload_path' => $uploadPath,
                'file_name' => $photo->getName(),
                'file_size' => $photo->getSize(),
                'mime_type' => $photo->getMimeType()
            ];

            // Generate unique filename
            $newName = $applicant_id . '_' . time() . '_' . $photo->getRandomName();
            $debug['new_name'] = $newName;

            // Move file to uploads directory
            if ($photo->move($uploadPath, $newName)) {
                // Verify file was created
                $debug['file_created'] = file_exists($uploadPath . '/' . $newName);

                // Delete old photo if exists
                $applicant = $this->applicantsModel->find($applicant_id);
                if (!empty($applicant['id_photo_path'])) {
                    $oldPhotoPath = $applicant['id_photo_path'];
                    $debug['old_photo_path'] = $oldPhotoPath;
                    $debug['old_photo_exists'] = file_exists($oldPhotoPath);

                    if (file_exists(ROOTPATH . $oldPhotoPath)) {
                        unlink(ROOTPATH . $oldPhotoPath);
                        $debug['old_photo_deleted'] = true;
                    }
                }

                // Update database with new photo path - store path WITH 'public/' prefix for easy view retrieval
                $this->applicantsModel->update($applicant_id, [
                    'id_photo_path' => 'public/uploads/photos/' . $newName,
                    'updated_by' => $applicant_id
                ]);

                $debug['db_updated'] = true;
                $debug['path_stored'] = 'public/uploads/photos/' . $newName;

                $response = [
                    'success' => true,
                    'message' => 'Photo updated successfully',
                    'path' => base_url('public/uploads/photos/' . $newName),
                    'debug' => $debug
                ];
            } else {
                $response = [
                    'success' => false,
                    'message' => 'Error uploading photo',
                    'debug' => array_merge($debug, [
                        'move_error' => $photo->getError(),
                        'error_code' => $photo->getError()
                    ])
                ];
            }
        } catch (\Exception $e) {
            $response = [
                'success' => false,
                'message' => 'Error uploading photo: ' . $e->getMessage(),
                'debug' => $debug
            ];
        }

        // Return JSON response
        return $this->response->setJSON($response);
    }

    public function updatePersonal()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setJSON(['success' => false, 'message' => 'Invalid request']);
        }

        $applicant_id = session()->get('applicant_id');

        $data = [
            'fname' => $this->request->getPost('fname'),
            'lname' => $this->request->getPost('lname'),
            'gender' => $this->request->getPost('gender'),
            'dobirth' => $this->request->getPost('dobirth'),
            'contact_details' => $this->request->getPost('contact_details'),
            'location_address' => $this->request->getPost('location_address'),
            'place_of_origin' => $this->request->getPost('place_of_origin'),
            'citizenship' => $this->request->getPost('citizenship'),
            'updated_by' => $applicant_id
        ];

        try {
            $this->applicantsModel->update($applicant_id, $data);
            return $this->response->setJSON([
                'success' => true,
                'message' => 'Personal information updated successfully',
                'csrf_hash' => csrf_hash()
            ]);
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Error updating information: ' . $e->getMessage(),
                'csrf_hash' => csrf_hash()
            ]);
        }
    }

    public function updateDocuments()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setJSON(['success' => false, 'message' => 'Invalid request']);
        }

        $applicant_id = session()->get('applicant_id');

        $data = [
            'id_numbers' => $this->request->getPost('id_numbers'),
            'offence_convicted' => $this->request->getPost('offence_convicted'),
            'updated_by' => $applicant_id
        ];

        try {
            $this->applicantsModel->update($applicant_id, $data);
            return $this->response->setJSON([
                'success' => true,
                'message' => 'Documents updated successfully',
                'csrf_hash' => csrf_hash()
            ]);
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Error updating documents: ' . $e->getMessage(),
                'csrf_hash' => csrf_hash()
            ]);
        }
    }

    public function updateEmployment()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setJSON(['success' => false, 'message' => 'Invalid request']);
        }

        $applicant_id = session()->get('applicant_id');

        $data = [
            'current_employer' => $this->request->getPost('current_employer'),
            'current_position' => $this->request->getPost('current_position'),
            'current_salary' => $this->request->getPost('current_salary'),
            'how_did_you_hear_about_us' => $this->request->getPost('how_did_you_hear_about_us'),
            'updated_by' => $applicant_id
        ];

        try {
            $this->applicantsModel->update($applicant_id, $data);
            return $this->response->setJSON([
                'success' => true,
                'message' => 'Employment information updated successfully',
                'csrf_hash' => csrf_hash()
            ]);
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Error updating employment information: ' . $e->getMessage(),
                'csrf_hash' => csrf_hash()
            ]);
        }
    }

    public function updateFamily()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setJSON(['success' => false, 'message' => 'Invalid request']);
        }

        $applicant_id = session()->get('applicant_id');

        $data = [
            'marital_status' => $this->request->getPost('marital_status'),
            'date_of_marriage' => $this->request->getPost('date_of_marriage'),
            'spouse_employer' => $this->request->getPost('spouse_employer'),
            'children' => json_encode($this->request->getPost('children') ?? []),
            'updated_by' => $applicant_id
        ];

        try {
            $this->applicantsModel->update($applicant_id, $data);
            return $this->response->setJSON([
                'success' => true,
                'message' => 'Family information updated successfully',
                'csrf_hash' => csrf_hash()
            ]);
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Error updating family information: ' . $e->getMessage(),
                'csrf_hash' => csrf_hash()
            ]);
        }
    }

    public function updateAdditional()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setJSON(['success' => false, 'message' => 'Invalid request']);
        }

        $applicant_id = session()->get('applicant_id');

        $data = [
            'publications' => $this->request->getPost('publications'),
            'awards' => $this->request->getPost('awards'),
            'referees' => $this->request->getPost('referees'),
            'updated_by' => $applicant_id
        ];

        try {
            $this->applicantsModel->update($applicant_id, $data);
            return $this->response->setJSON([
                'success' => true,
                'message' => 'Additional information updated successfully',
                'csrf_hash' => csrf_hash()
            ]);
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Error updating additional information: ' . $e->getMessage(),
                'csrf_hash' => csrf_hash()
            ]);
        }
    }

    public function changePassword()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setJSON(['success' => false, 'message' => 'Invalid request']);
        }

        $applicant_id = session()->get('applicant_id');
        $current_password = $this->request->getPost('current_password');
        $new_password = $this->request->getPost('new_password');
        $confirm_password = $this->request->getPost('confirm_password');

        // Get current applicant data
        $applicant = $this->applicantsModel->find($applicant_id);

        // Verify current password
        if (!password_verify($current_password, $applicant['password'])) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Current password is incorrect'
            ]);
        }

        // Verify new passwords match
        if ($new_password !== $confirm_password) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'New passwords do not match'
            ]);
        }

        try {
            $this->applicantsModel->update($applicant_id, [
                'password' => $new_password,
                'updated_by' => $applicant_id
            ]);

            return $this->response->setJSON([
                'success' => true,
                'message' => 'Password changed successfully'
            ]);
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Error changing password: ' . $e->getMessage()
            ]);
        }
    }
}
