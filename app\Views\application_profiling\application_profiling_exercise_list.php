<?= $this->extend('templates/nolstemp') ?>

<?= $this->section('content') ?>

<div class="container-fluid px-4">
    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
            <h1 class="m-0 font-weight-bold text-primary"><?= esc($title ?? 'Profiling Exercises') ?></h1>
        </div>
        <div class="card-body">
            <?php if (session()->has('message')): ?>
                <div class="alert alert-success" role="alert">
                    <?= session('message') ?>
                </div>
            <?php endif; ?>
            <?php if (session()->has('error')): ?>
                <div class="alert alert-danger" role="alert">
                    <?= session('error') ?>
                </div>
            <?php endif; ?>

            <p class="mb-4">Select an exercise to view its position groups for profiling.</p>

            <?php if (!empty($exercises) && is_array($exercises)): ?>
                <div class="row">
                    <?php foreach ($exercises as $exercise): ?>
                        <div class="col-lg-4 col-md-6 mb-4">
                            <div class="card h-100 border-left-primary">
                                <div class="card-body">
                                    <div class="row no-gutters align-items-center">
                                        <div class="col mr-2">
                                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                                Exercise
                                            </div>
                                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                                <?= esc($exercise['exercise_name']) ?>
                                            </div>
                                            <div class="text-xs text-gray-600 mt-2">
                                                <strong>Advertisement:</strong> <?= esc($exercise['advertisement_no']) ?><br>
                                                <strong>Published:</strong> <?= esc(date('d M Y', strtotime($exercise['publish_date_from']))) ?> - <?= esc(date('d M Y', strtotime($exercise['publish_date_to']))) ?><br>
                                                <strong>Status:</strong> 
                                                <span class="badge badge-<?= $exercise['status'] == 'selection' ? 'success' : 'secondary' ?>">
                                                    <?= ucfirst(esc($exercise['status'])) ?>
                                                </span>
                                            </div>
                                            <div class="mt-3">
                                                <p class="text-sm text-gray-700 mb-2">
                                                    <?= esc($exercise['description']) ?>
                                                </p>
                                            </div>
                                        </div>
                                        <div class="col-auto">
                                            <i class="fas fa-clipboard-list fa-2x text-gray-300"></i>
                                        </div>
                                    </div>
                                </div>
                                <div class="card-footer">
                                    <a href="<?= base_url('profile_applications_exercise/profile_exercise/' . $exercise['id']) ?>" 
                                       class="btn btn-primary btn-sm btn-block">
                                        <i class="fas fa-eye me-1"></i> View Position Groups
                                    </a>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php else: ?>
                <div class="text-center py-5">
                    <i class="fas fa-clipboard-list fa-3x text-gray-300 mb-3"></i>
                    <h4 class="text-gray-500">No Exercises Available</h4>
                    <p class="text-gray-400">There are currently no exercises available for profiling.</p>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
    $(document).ready(function() {
        // Add any specific JavaScript for this page
        console.log('Exercise list page loaded');
    });
</script>
<?= $this->endSection() ?>
