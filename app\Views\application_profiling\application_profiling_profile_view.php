<?= $this->extend('templates/nolstemp') ?>

<?= $this->section('content') ?>

<div class="container-fluid px-4">
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="<?= base_url('profile_applications_exercise') ?>">Profiling Exercises</a></li>
            <li class="breadcrumb-item"><a href="<?= base_url('profile_applications_exercise/profile_exercise/' . ($application['exercise_id'] ?? '#')) ?>"><?= esc($application['exercise_name'] ?? 'Exercise') ?></a></li>
            <li class="breadcrumb-item"><a href="<?= base_url('profile_applications_exercise/profile_position/' . ($application['position_id'] ?? '#')) ?>">Applications</a></li>
            <li class="breadcrumb-item active" aria-current="page"><?= esc($application['first_name'] . ' ' . $application['last_name']) ?></li>
        </ol>
    </nav>

    <div class="row">
        <!-- Application Summary Card -->
        <div class="col-12 mb-4">
            <div class="card shadow">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h1 class="m-0 font-weight-bold text-primary"><?= esc($title ?? 'Applicant Profile') ?></h1>
                    <div>
                        <a href="<?= base_url('profile_applications_exercise/profile_position/' . ($application['position_id'] ?? '#')) ?>" class="btn btn-sm btn-secondary me-2">
                            <i class="fas fa-arrow-left me-1"></i> Back to Applications
                        </a>
                        <button class="btn btn-sm btn-primary" onclick="window.print()">
                            <i class="fas fa-print me-1"></i> Print Profile
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-8">
                            <h4 class="text-primary"><?= esc($application['first_name'] . ' ' . $application['last_name']) ?></h4>
                            <p class="text-muted mb-3">
                                <strong>Application #:</strong> <?= esc($application['application_number']) ?><br>
                                <strong>Position:</strong> <?= esc($application['position_designation']) ?> (<?= esc($application['position_reference']) ?>)<br>
                                <strong>Submitted:</strong> <?= esc(date('d M Y H:i', strtotime($application['created_at']))) ?>
                            </p>
                        </div>
                        <div class="col-md-4 text-end">
                            <div class="text-center">
                                <i class="fas fa-user-circle fa-4x text-gray-300 mb-2"></i>
                                <br>
                                <span class="badge badge-success">Pre-screened</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Personal Information -->
        <div class="col-lg-6 mb-4">
            <div class="card shadow h-100">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Personal Information</h6>
                </div>
                <div class="card-body">
                    <table class="table table-borderless table-sm">
                        <tr>
                            <td><strong>Full Name:</strong></td>
                            <td><?= esc($profile['name']) ?></td>
                        </tr>
                        <tr>
                            <td><strong>Gender:</strong></td>
                            <td><?= esc($profile['sex']) ?></td>
                        </tr>
                        <tr>
                            <td><strong>Age:</strong></td>
                            <td><?= esc($profile['age']) ?> years</td>
                        </tr>
                        <tr>
                            <td><strong>Place of Origin:</strong></td>
                            <td><?= esc($profile['place_origin']) ?></td>
                        </tr>
                        <tr>
                            <td><strong>Contact Details:</strong></td>
                            <td><?= esc($profile['contact_details']) ?></td>
                        </tr>
                        <tr>
                            <td><strong>NID Number:</strong></td>
                            <td><?= esc($profile['nid_number']) ?></td>
                        </tr>
                        <tr>
                            <td><strong>Address:</strong></td>
                            <td><?= esc($profile['address_location']) ?></td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>

        <!-- Current Employment -->
        <div class="col-lg-6 mb-4">
            <div class="card shadow h-100">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Current Employment</h6>
                </div>
                <div class="card-body">
                    <table class="table table-borderless table-sm">
                        <tr>
                            <td><strong>Employer:</strong></td>
                            <td><?= esc($profile['current_employer']) ?></td>
                        </tr>
                        <tr>
                            <td><strong>Position:</strong></td>
                            <td><?= esc($profile['current_position']) ?></td>
                        </tr>
                        <tr>
                            <td><strong>Citizenship:</strong></td>
                            <td><?= esc($application['citizenship']) ?></td>
                        </tr>
                        <tr>
                            <td><strong>Marital Status:</strong></td>
                            <td><?= esc($application['marital_status']) ?></td>
                        </tr>
                        <tr>
                            <td><strong>Current Salary:</strong></td>
                            <td><?= esc($application['current_salary']) ?></td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>

        <!-- Qualifications -->
        <div class="col-12 mb-4">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Qualifications</h6>
                </div>
                <div class="card-body">
                    <p><?= nl2br(esc($profile['qualification_text'])) ?></p>
                </div>
            </div>
        </div>

        <!-- Other Training -->
        <div class="col-12 mb-4">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Other Training & Certifications</h6>
                </div>
                <div class="card-body">
                    <p><?= nl2br(esc($profile['other_trainings'])) ?></p>
                </div>
            </div>
        </div>

        <!-- Knowledge -->
        <div class="col-12 mb-4">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Knowledge</h6>
                </div>
                <div class="card-body">
                    <p><?= nl2br(esc($profile['knowledge'])) ?></p>
                </div>
            </div>
        </div>

        <!-- Skills & Competencies -->
        <div class="col-12 mb-4">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Skills & Competencies</h6>
                </div>
                <div class="card-body">
                    <p><?= nl2br(esc($profile['skills_competencies'])) ?></p>
                </div>
            </div>
        </div>

        <!-- Job Experience -->
        <div class="col-12 mb-4">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Job Experience</h6>
                </div>
                <div class="card-body">
                    <p><?= nl2br(esc($profile['job_experiences'])) ?></p>
                </div>
            </div>
        </div>

        <!-- Publications -->
        <?php if (!empty($profile['publications'])): ?>
        <div class="col-lg-6 mb-4">
            <div class="card shadow h-100">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Publications</h6>
                </div>
                <div class="card-body">
                    <p><?= nl2br(esc($profile['publications'])) ?></p>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <!-- Awards -->
        <?php if (!empty($profile['awards'])): ?>
        <div class="col-lg-6 mb-4">
            <div class="card shadow h-100">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Awards & Recognition</h6>
                </div>
                <div class="card-body">
                    <p><?= nl2br(esc($profile['awards'])) ?></p>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <!-- Referees -->
        <div class="col-12 mb-4">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Referees</h6>
                </div>
                <div class="card-body">
                    <p><?= nl2br(esc($profile['referees'])) ?></p>
                </div>
            </div>
        </div>

        <!-- Assessment Comments -->
        <div class="col-lg-6 mb-4">
            <div class="card shadow h-100">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Assessment Comments</h6>
                </div>
                <div class="card-body">
                    <p><?= nl2br(esc($profile['comments'])) ?></p>
                </div>
            </div>
        </div>

        <!-- Remarks -->
        <div class="col-lg-6 mb-4">
            <div class="card shadow h-100">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Remarks</h6>
                </div>
                <div class="card-body">
                    <p><?= nl2br(esc($profile['remarks'])) ?></p>
                </div>
            </div>
        </div>

        <!-- Profile Metadata -->
        <div class="col-12 mb-4">
            <div class="card shadow border-left-info">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <small class="text-muted">
                                <strong>Profile Created:</strong> <?= esc(date('d M Y H:i', strtotime($profile['created_at']))) ?>
                            </small>
                        </div>
                        <div class="col-md-6 text-end">
                            <small class="text-muted">
                                <strong>Last Updated:</strong> <?= esc(date('d M Y H:i', strtotime($profile['updated_at']))) ?>
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
    $(document).ready(function() {
        // Add any specific JavaScript for this page
        console.log('Profile view page loaded');

        // Print functionality
        window.print = function() {
            window.print();
        };
    });
</script>
<?= $this->endSection() ?>
