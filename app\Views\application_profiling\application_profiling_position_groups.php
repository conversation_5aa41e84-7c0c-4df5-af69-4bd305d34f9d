<?= $this->extend('templates/nolstemp') ?>

<?= $this->section('content') ?>

<div class="container-fluid px-4">
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="<?= base_url('profile_applications_exercise') ?>">Profiling Exercises</a></li>
            <li class="breadcrumb-item active" aria-current="page"><?= esc($exercise['exercise_name'] ?? 'Exercise Details') ?></li>
        </ol>
    </nav>

    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
            <h1 class="m-0 font-weight-bold text-primary"><?= esc($title ?? 'Position Groups') ?></h1>
            <a href="<?= base_url('profile_applications_exercise') ?>" class="btn btn-sm btn-secondary">
                <i class="fas fa-arrow-left me-1"></i> Back to Exercises
            </a>
        </div>
        <div class="card-body">
            <?php if (session()->has('message')): ?>
                <div class="alert alert-success" role="alert">
                    <?= session('message') ?>
                </div>
            <?php endif; ?>
            <?php if (session()->has('error')): ?>
                <div class="alert alert-danger" role="alert">
                    <?= session('error') ?>
                </div>
            <?php endif; ?>

            <!-- Exercise Information -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card border-left-info">
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-8">
                                    <h5 class="card-title text-info"><?= esc($exercise['exercise_name']) ?></h5>
                                    <p class="card-text">
                                        <strong>Advertisement No:</strong> <?= esc($exercise['advertisement_no']) ?><br>
                                        <strong>Status:</strong> 
                                        <span class="badge badge-<?= $exercise['status'] == 'selection' ? 'success' : 'secondary' ?>">
                                            <?= ucfirst(esc($exercise['status'])) ?>
                                        </span>
                                    </p>
                                </div>
                                <div class="col-md-4 text-end">
                                    <i class="fas fa-clipboard-list fa-3x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <p class="mb-4">Select a position group to view its positions for profiling.</p>

            <?php if (!empty($position_groups) && is_array($position_groups)): ?>
                <div class="row">
                    <?php foreach ($position_groups as $group): ?>
                        <div class="col-lg-4 col-md-6 mb-4">
                            <div class="card h-100 border-left-success">
                                <div class="card-body">
                                    <div class="row no-gutters align-items-center">
                                        <div class="col mr-2">
                                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                                Position Group
                                            </div>
                                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                                <?= esc($group['group_name']) ?>
                                            </div>
                                            <div class="text-xs text-gray-600 mt-2">
                                                <?= esc($group['group_description']) ?>
                                            </div>
                                            <div class="mt-3">
                                                <div class="row text-center">
                                                    <div class="col-6">
                                                        <div class="text-xs font-weight-bold text-uppercase text-primary">Positions</div>
                                                        <div class="h6 mb-0 font-weight-bold text-primary"><?= esc($group['positions_count']) ?></div>
                                                    </div>
                                                    <div class="col-6">
                                                        <div class="text-xs font-weight-bold text-uppercase text-info">Applications</div>
                                                        <div class="h6 mb-0 font-weight-bold text-info"><?= esc($group['applications_count']) ?></div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-auto">
                                            <i class="fas fa-users fa-2x text-gray-300"></i>
                                        </div>
                                    </div>
                                </div>
                                <div class="card-footer">
                                    <a href="<?= base_url('profile_applications_exercise/profile_group/' . $group['id']) ?>" 
                                       class="btn btn-success btn-sm btn-block">
                                        <i class="fas fa-eye me-1"></i> View Positions
                                    </a>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php else: ?>
                <div class="text-center py-5">
                    <i class="fas fa-users fa-3x text-gray-300 mb-3"></i>
                    <h4 class="text-gray-500">No Position Groups Available</h4>
                    <p class="text-gray-400">There are currently no position groups available for this exercise.</p>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
    $(document).ready(function() {
        // Add any specific JavaScript for this page
        console.log('Position groups page loaded');
    });
</script>
<?= $this->endSection() ?>
