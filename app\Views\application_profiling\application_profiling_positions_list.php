<?= $this->extend('templates/nolstemp') ?>

<?= $this->section('content') ?>

<div class="container-fluid px-4">
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="<?= base_url('profile_applications_exercise') ?>">Profiling Exercises</a></li>
            <li class="breadcrumb-item"><a href="<?= base_url('profile_applications_exercise/profile_exercise/' . ($position_group['exercise_id'] ?? '#')) ?>"><?= esc($position_group['exercise_name'] ?? 'Exercise') ?></a></li> 
            <li class="breadcrumb-item active" aria-current="page"><?= esc($position_group['group_name'] ?? 'Position Group') ?></li>
        </ol>
    </nav>

    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
            <h1 class="m-0 font-weight-bold text-primary"><?= esc($title ?? 'Positions') ?></h1>
            <a href="<?= base_url('profile_applications_exercise/profile_exercise/' . ($position_group['exercise_id'] ?? '#')) ?>" class="btn btn-sm btn-secondary">
                <i class="fas fa-arrow-left me-1"></i> Back to Position Groups
            </a>
        </div>
        <div class="card-body">
            <?php if (session()->has('message')): ?>
                <div class="alert alert-success" role="alert">
                    <?= session('message') ?>
                </div>
            <?php endif; ?>
            <?php if (session()->has('error')): ?>
                <div class="alert alert-danger" role="alert">
                    <?= session('error') ?>
                </div>
            <?php endif; ?>

            <!-- Position Group Information -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card border-left-success">
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-8">
                                    <h5 class="card-title text-success"><?= esc($position_group['group_name']) ?></h5>
                                    <p class="card-text">
                                        <strong>Exercise:</strong> <?= esc($position_group['exercise_name']) ?><br>
                                        <strong>Description:</strong> <?= esc($position_group['group_description']) ?>
                                    </p>
                                </div>
                                <div class="col-md-4 text-end">
                                    <i class="fas fa-briefcase fa-3x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <p class="mb-4">Select a position to view applications for profiling.</p>

            <?php if (!empty($positions) && is_array($positions)): ?>
                <div class="table-responsive">
                    <table class="table table-bordered table-striped" id="positions-table" width="100%" cellspacing="0">
                        <thead>
                            <tr>
                                <th>Reference</th>
                                <th>Designation</th>
                                <th>Classification</th>
                                <th>Location</th>
                                <th>Salary Range</th>
                                <th>Applications</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($positions as $position): ?>
                                <tr>
                                    <td><?= esc($position['position_reference']) ?></td>
                                    <td>
                                        <strong><?= esc($position['designation']) ?></strong>
                                        <br>
                                        <small class="text-muted"><?= esc($position['award']) ?></small>
                                    </td>
                                    <td><?= esc($position['classification']) ?></td>
                                    <td><?= esc($position['location']) ?></td>
                                    <td><?= esc($position['annual_salary']) ?></td>
                                    <td>
                                        <span class="badge badge-info badge-pill">
                                            <?= esc($position['applications_count']) ?> Applications
                                        </span>
                                    </td>
                                    <td>
                                        <a href="<?= base_url('profile_applications_exercise/profile_position/' . $position['id']) ?>" 
                                           class="btn btn-primary btn-sm" title="View Applications">
                                            <i class="fas fa-users"></i> View Applications
                                        </a>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php else: ?>
                <div class="text-center py-5">
                    <i class="fas fa-briefcase fa-3x text-gray-300 mb-3"></i>
                    <h4 class="text-gray-500">No Positions Available</h4>
                    <p class="text-gray-400">There are currently no positions available for this position group.</p>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<!-- Include DataTables if needed -->
<link href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css" rel="stylesheet">
<script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>

<script>
    $(document).ready(function() {
        $('#positions-table').DataTable({
            "order": [[ 0, "asc" ]], // Order by reference ascending by default
            "pageLength": 25,
            "responsive": true
        });
    });
</script>
<?= $this->endSection() ?>
